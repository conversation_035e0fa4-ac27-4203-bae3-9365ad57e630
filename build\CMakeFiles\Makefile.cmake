# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/mnt/c/Users/<USER>/Desktop/car/CMakeLists.txt"
  "CMakeFiles/3.28.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.28.3/CMakeSystem.cmake"
  "_deps/httplib-src/CMakeLists.txt"
  "_deps/httplib-src/cmake/FindBrotli.cmake"
  "_deps/httplib-src/httplibConfig.cmake.in"
  "_deps/json-src/CMakeLists.txt"
  "_deps/json-src/cmake/config.cmake.in"
  "_deps/json-src/cmake/nlohmann_jsonConfigVersion.cmake.in"
  "_deps/json-src/cmake/pkg-config.pc.in"
  "/usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfigVersion.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/BoostDetectToolset-1.83.0.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_atomic-1.83.0/boost_atomic-config-version.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_atomic-1.83.0/boost_atomic-config.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_atomic-1.83.0/libboost_atomic-variant-shared.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_atomic-1.83.0/libboost_atomic-variant-static.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_filesystem-1.83.0/boost_filesystem-config-version.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_filesystem-1.83.0/boost_filesystem-config.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_filesystem-1.83.0/libboost_filesystem-variant-shared.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_filesystem-1.83.0/libboost_filesystem-variant-static.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_headers-1.83.0/boost_headers-config-version.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_headers-1.83.0/boost_headers-config.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_system-1.83.0/boost_system-config-version.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_system-1.83.0/boost_system-config.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_system-1.83.0/libboost_system-variant-shared.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_system-1.83.0/libboost_system-variant-static.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_thread-1.83.0/boost_thread-config-version.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_thread-1.83.0/boost_thread-config.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_thread-1.83.0/libboost_thread-variant-shared.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_thread-1.83.0/libboost_thread-variant-static.cmake"
  "/usr/local/lib/cmake/vsomeip3/vsomeip3Config.cmake"
  "/usr/local/lib/cmake/vsomeip3/vsomeip3ConfigVersion.cmake"
  "/usr/local/lib/cmake/vsomeip3/vsomeip3Targets-relwithdebinfo.cmake"
  "/usr/local/lib/cmake/vsomeip3/vsomeip3Targets.cmake"
  "/usr/share/cmake-3.28/Modules/BasicConfigVersion-SameMinorVersion.cmake.in"
  "/usr/share/cmake-3.28/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakePackageConfigHelpers.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.28/Modules/CheckCXXSourceCompiles.cmake"
  "/usr/share/cmake-3.28/Modules/CheckIncludeFileCXX.cmake"
  "/usr/share/cmake-3.28/Modules/CheckLibraryExists.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.28/Modules/ExternalProject.cmake"
  "/usr/share/cmake-3.28/Modules/ExternalProject/shared_internal_commands.cmake"
  "/usr/share/cmake-3.28/Modules/FetchContent.cmake"
  "/usr/share/cmake-3.28/Modules/FetchContent/CMakeLists.cmake.in"
  "/usr/share/cmake-3.28/Modules/FindBoost.cmake"
  "/usr/share/cmake-3.28/Modules/FindGit.cmake"
  "/usr/share/cmake-3.28/Modules/FindOpenSSL.cmake"
  "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"
  "/usr/share/cmake-3.28/Modules/FindPkgConfig.cmake"
  "/usr/share/cmake-3.28/Modules/FindThreads.cmake"
  "/usr/share/cmake-3.28/Modules/FindZLIB.cmake"
  "/usr/share/cmake-3.28/Modules/GNUInstallDirs.cmake"
  "/usr/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux-Initialize.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/UnixPaths.cmake"
  "/usr/share/cmake-3.28/Modules/SelectLibraryConfigurations.cmake"
  "/usr/share/cmake-3.28/Modules/WriteBasicConfigVersionFile.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "_deps/httplib-subbuild/CMakeLists.txt"
  "_deps/json-subbuild/CMakeLists.txt"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "_deps/httplib-build/httplibConfig.cmake"
  "_deps/httplib-build/httplibConfigVersion.cmake"
  "_deps/httplib-build/CMakeFiles/CMakeDirectoryInformation.cmake"
  "_deps/json-build/nlohmann_json.pc"
  "_deps/json-build/nlohmann_jsonConfigVersion.cmake"
  "_deps/json-build/nlohmann_jsonConfig.cmake"
  "_deps/json-build/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/body_controller_lib.dir/DependInfo.cmake"
  "CMakeFiles/test_door_client.dir/DependInfo.cmake"
  "CMakeFiles/test_window_client.dir/DependInfo.cmake"
  "CMakeFiles/test_light_client.dir/DependInfo.cmake"
  "CMakeFiles/test_seat_client.dir/DependInfo.cmake"
  "CMakeFiles/body_controller_web_server.dir/DependInfo.cmake"
  )

# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/c/Users/<USER>/Desktop/car

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/c/Users/<USER>/Desktop/car/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/body_controller_lib.dir/all
all: CMakeFiles/test_door_client.dir/all
all: CMakeFiles/test_window_client.dir/all
all: CMakeFiles/test_light_client.dir/all
all: CMakeFiles/test_seat_client.dir/all
all: CMakeFiles/body_controller_web_server.dir/all
all: _deps/httplib-build/all
all: _deps/json-build/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: _deps/httplib-build/preinstall
preinstall: _deps/json-build/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/body_controller_lib.dir/clean
clean: CMakeFiles/test_door_client.dir/clean
clean: CMakeFiles/test_window_client.dir/clean
clean: CMakeFiles/test_light_client.dir/clean
clean: CMakeFiles/test_seat_client.dir/clean
clean: CMakeFiles/body_controller_web_server.dir/clean
clean: _deps/httplib-build/clean
clean: _deps/json-build/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory _deps/httplib-build

# Recursive "all" directory target.
_deps/httplib-build/all:
.PHONY : _deps/httplib-build/all

# Recursive "preinstall" directory target.
_deps/httplib-build/preinstall:
.PHONY : _deps/httplib-build/preinstall

# Recursive "clean" directory target.
_deps/httplib-build/clean:
.PHONY : _deps/httplib-build/clean

#=============================================================================
# Directory level rules for directory _deps/json-build

# Recursive "all" directory target.
_deps/json-build/all:
.PHONY : _deps/json-build/all

# Recursive "preinstall" directory target.
_deps/json-build/preinstall:
.PHONY : _deps/json-build/preinstall

# Recursive "clean" directory target.
_deps/json-build/clean:
.PHONY : _deps/json-build/clean

#=============================================================================
# Target rules for target CMakeFiles/body_controller_lib.dir

# All Build rule for target.
CMakeFiles/body_controller_lib.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_lib.dir/build.make CMakeFiles/body_controller_lib.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_lib.dir/build.make CMakeFiles/body_controller_lib.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles --progress-num=1,2,3,4,5,6 "Built target body_controller_lib"
.PHONY : CMakeFiles/body_controller_lib.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/body_controller_lib.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles 6
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/body_controller_lib.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles 0
.PHONY : CMakeFiles/body_controller_lib.dir/rule

# Convenience name for target.
body_controller_lib: CMakeFiles/body_controller_lib.dir/rule
.PHONY : body_controller_lib

# clean rule for target.
CMakeFiles/body_controller_lib.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_lib.dir/build.make CMakeFiles/body_controller_lib.dir/clean
.PHONY : CMakeFiles/body_controller_lib.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_door_client.dir

# All Build rule for target.
CMakeFiles/test_door_client.dir/all: CMakeFiles/body_controller_lib.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_door_client.dir/build.make CMakeFiles/test_door_client.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_door_client.dir/build.make CMakeFiles/test_door_client.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles --progress-num=13,14 "Built target test_door_client"
.PHONY : CMakeFiles/test_door_client.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_door_client.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles 8
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_door_client.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles 0
.PHONY : CMakeFiles/test_door_client.dir/rule

# Convenience name for target.
test_door_client: CMakeFiles/test_door_client.dir/rule
.PHONY : test_door_client

# clean rule for target.
CMakeFiles/test_door_client.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_door_client.dir/build.make CMakeFiles/test_door_client.dir/clean
.PHONY : CMakeFiles/test_door_client.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_window_client.dir

# All Build rule for target.
CMakeFiles/test_window_client.dir/all: CMakeFiles/body_controller_lib.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_window_client.dir/build.make CMakeFiles/test_window_client.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_window_client.dir/build.make CMakeFiles/test_window_client.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles --progress-num=19,20 "Built target test_window_client"
.PHONY : CMakeFiles/test_window_client.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_window_client.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles 8
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_window_client.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles 0
.PHONY : CMakeFiles/test_window_client.dir/rule

# Convenience name for target.
test_window_client: CMakeFiles/test_window_client.dir/rule
.PHONY : test_window_client

# clean rule for target.
CMakeFiles/test_window_client.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_window_client.dir/build.make CMakeFiles/test_window_client.dir/clean
.PHONY : CMakeFiles/test_window_client.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_light_client.dir

# All Build rule for target.
CMakeFiles/test_light_client.dir/all: CMakeFiles/body_controller_lib.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_light_client.dir/build.make CMakeFiles/test_light_client.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_light_client.dir/build.make CMakeFiles/test_light_client.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles --progress-num=15,16 "Built target test_light_client"
.PHONY : CMakeFiles/test_light_client.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_light_client.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles 8
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_light_client.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles 0
.PHONY : CMakeFiles/test_light_client.dir/rule

# Convenience name for target.
test_light_client: CMakeFiles/test_light_client.dir/rule
.PHONY : test_light_client

# clean rule for target.
CMakeFiles/test_light_client.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_light_client.dir/build.make CMakeFiles/test_light_client.dir/clean
.PHONY : CMakeFiles/test_light_client.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_seat_client.dir

# All Build rule for target.
CMakeFiles/test_seat_client.dir/all: CMakeFiles/body_controller_lib.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_seat_client.dir/build.make CMakeFiles/test_seat_client.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_seat_client.dir/build.make CMakeFiles/test_seat_client.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles --progress-num=17,18 "Built target test_seat_client"
.PHONY : CMakeFiles/test_seat_client.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_seat_client.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles 8
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_seat_client.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles 0
.PHONY : CMakeFiles/test_seat_client.dir/rule

# Convenience name for target.
test_seat_client: CMakeFiles/test_seat_client.dir/rule
.PHONY : test_seat_client

# clean rule for target.
CMakeFiles/test_seat_client.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_seat_client.dir/build.make CMakeFiles/test_seat_client.dir/clean
.PHONY : CMakeFiles/test_seat_client.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/body_controller_web_server.dir

# All Build rule for target.
CMakeFiles/body_controller_web_server.dir/all: CMakeFiles/body_controller_lib.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_web_server.dir/build.make CMakeFiles/body_controller_web_server.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_web_server.dir/build.make CMakeFiles/body_controller_web_server.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles --progress-num=7,8,9,10,11,12 "Built target body_controller_web_server"
.PHONY : CMakeFiles/body_controller_web_server.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/body_controller_web_server.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles 12
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/body_controller_web_server.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles 0
.PHONY : CMakeFiles/body_controller_web_server.dir/rule

# Convenience name for target.
body_controller_web_server: CMakeFiles/body_controller_web_server.dir/rule
.PHONY : body_controller_web_server

# clean rule for target.
CMakeFiles/body_controller_web_server.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_web_server.dir/build.make CMakeFiles/body_controller_web_server.dir/clean
.PHONY : CMakeFiles/body_controller_web_server.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system


# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/c/Users/<USER>/Desktop/car

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/c/Users/<USER>/Desktop/car/build

# Include any dependencies generated for this target.
include CMakeFiles/body_controller_lib.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/body_controller_lib.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/body_controller_lib.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/body_controller_lib.dir/flags.make

CMakeFiles/body_controller_lib.dir/src/communication/someip_client.cpp.o: CMakeFiles/body_controller_lib.dir/flags.make
CMakeFiles/body_controller_lib.dir/src/communication/someip_client.cpp.o: /mnt/c/Users/<USER>/Desktop/car/src/communication/someip_client.cpp
CMakeFiles/body_controller_lib.dir/src/communication/someip_client.cpp.o: CMakeFiles/body_controller_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/body_controller_lib.dir/src/communication/someip_client.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/body_controller_lib.dir/src/communication/someip_client.cpp.o -MF CMakeFiles/body_controller_lib.dir/src/communication/someip_client.cpp.o.d -o CMakeFiles/body_controller_lib.dir/src/communication/someip_client.cpp.o -c /mnt/c/Users/<USER>/Desktop/car/src/communication/someip_client.cpp

CMakeFiles/body_controller_lib.dir/src/communication/someip_client.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/body_controller_lib.dir/src/communication/someip_client.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/c/Users/<USER>/Desktop/car/src/communication/someip_client.cpp > CMakeFiles/body_controller_lib.dir/src/communication/someip_client.cpp.i

CMakeFiles/body_controller_lib.dir/src/communication/someip_client.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/body_controller_lib.dir/src/communication/someip_client.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/c/Users/<USER>/Desktop/car/src/communication/someip_client.cpp -o CMakeFiles/body_controller_lib.dir/src/communication/someip_client.cpp.s

CMakeFiles/body_controller_lib.dir/src/communication/door_service_client.cpp.o: CMakeFiles/body_controller_lib.dir/flags.make
CMakeFiles/body_controller_lib.dir/src/communication/door_service_client.cpp.o: /mnt/c/Users/<USER>/Desktop/car/src/communication/door_service_client.cpp
CMakeFiles/body_controller_lib.dir/src/communication/door_service_client.cpp.o: CMakeFiles/body_controller_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/body_controller_lib.dir/src/communication/door_service_client.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/body_controller_lib.dir/src/communication/door_service_client.cpp.o -MF CMakeFiles/body_controller_lib.dir/src/communication/door_service_client.cpp.o.d -o CMakeFiles/body_controller_lib.dir/src/communication/door_service_client.cpp.o -c /mnt/c/Users/<USER>/Desktop/car/src/communication/door_service_client.cpp

CMakeFiles/body_controller_lib.dir/src/communication/door_service_client.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/body_controller_lib.dir/src/communication/door_service_client.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/c/Users/<USER>/Desktop/car/src/communication/door_service_client.cpp > CMakeFiles/body_controller_lib.dir/src/communication/door_service_client.cpp.i

CMakeFiles/body_controller_lib.dir/src/communication/door_service_client.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/body_controller_lib.dir/src/communication/door_service_client.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/c/Users/<USER>/Desktop/car/src/communication/door_service_client.cpp -o CMakeFiles/body_controller_lib.dir/src/communication/door_service_client.cpp.s

CMakeFiles/body_controller_lib.dir/src/communication/window_service_client.cpp.o: CMakeFiles/body_controller_lib.dir/flags.make
CMakeFiles/body_controller_lib.dir/src/communication/window_service_client.cpp.o: /mnt/c/Users/<USER>/Desktop/car/src/communication/window_service_client.cpp
CMakeFiles/body_controller_lib.dir/src/communication/window_service_client.cpp.o: CMakeFiles/body_controller_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/body_controller_lib.dir/src/communication/window_service_client.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/body_controller_lib.dir/src/communication/window_service_client.cpp.o -MF CMakeFiles/body_controller_lib.dir/src/communication/window_service_client.cpp.o.d -o CMakeFiles/body_controller_lib.dir/src/communication/window_service_client.cpp.o -c /mnt/c/Users/<USER>/Desktop/car/src/communication/window_service_client.cpp

CMakeFiles/body_controller_lib.dir/src/communication/window_service_client.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/body_controller_lib.dir/src/communication/window_service_client.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/c/Users/<USER>/Desktop/car/src/communication/window_service_client.cpp > CMakeFiles/body_controller_lib.dir/src/communication/window_service_client.cpp.i

CMakeFiles/body_controller_lib.dir/src/communication/window_service_client.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/body_controller_lib.dir/src/communication/window_service_client.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/c/Users/<USER>/Desktop/car/src/communication/window_service_client.cpp -o CMakeFiles/body_controller_lib.dir/src/communication/window_service_client.cpp.s

CMakeFiles/body_controller_lib.dir/src/communication/light_service_client.cpp.o: CMakeFiles/body_controller_lib.dir/flags.make
CMakeFiles/body_controller_lib.dir/src/communication/light_service_client.cpp.o: /mnt/c/Users/<USER>/Desktop/car/src/communication/light_service_client.cpp
CMakeFiles/body_controller_lib.dir/src/communication/light_service_client.cpp.o: CMakeFiles/body_controller_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/body_controller_lib.dir/src/communication/light_service_client.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/body_controller_lib.dir/src/communication/light_service_client.cpp.o -MF CMakeFiles/body_controller_lib.dir/src/communication/light_service_client.cpp.o.d -o CMakeFiles/body_controller_lib.dir/src/communication/light_service_client.cpp.o -c /mnt/c/Users/<USER>/Desktop/car/src/communication/light_service_client.cpp

CMakeFiles/body_controller_lib.dir/src/communication/light_service_client.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/body_controller_lib.dir/src/communication/light_service_client.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/c/Users/<USER>/Desktop/car/src/communication/light_service_client.cpp > CMakeFiles/body_controller_lib.dir/src/communication/light_service_client.cpp.i

CMakeFiles/body_controller_lib.dir/src/communication/light_service_client.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/body_controller_lib.dir/src/communication/light_service_client.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/c/Users/<USER>/Desktop/car/src/communication/light_service_client.cpp -o CMakeFiles/body_controller_lib.dir/src/communication/light_service_client.cpp.s

CMakeFiles/body_controller_lib.dir/src/communication/seat_service_client.cpp.o: CMakeFiles/body_controller_lib.dir/flags.make
CMakeFiles/body_controller_lib.dir/src/communication/seat_service_client.cpp.o: /mnt/c/Users/<USER>/Desktop/car/src/communication/seat_service_client.cpp
CMakeFiles/body_controller_lib.dir/src/communication/seat_service_client.cpp.o: CMakeFiles/body_controller_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/body_controller_lib.dir/src/communication/seat_service_client.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/body_controller_lib.dir/src/communication/seat_service_client.cpp.o -MF CMakeFiles/body_controller_lib.dir/src/communication/seat_service_client.cpp.o.d -o CMakeFiles/body_controller_lib.dir/src/communication/seat_service_client.cpp.o -c /mnt/c/Users/<USER>/Desktop/car/src/communication/seat_service_client.cpp

CMakeFiles/body_controller_lib.dir/src/communication/seat_service_client.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/body_controller_lib.dir/src/communication/seat_service_client.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/c/Users/<USER>/Desktop/car/src/communication/seat_service_client.cpp > CMakeFiles/body_controller_lib.dir/src/communication/seat_service_client.cpp.i

CMakeFiles/body_controller_lib.dir/src/communication/seat_service_client.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/body_controller_lib.dir/src/communication/seat_service_client.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/c/Users/<USER>/Desktop/car/src/communication/seat_service_client.cpp -o CMakeFiles/body_controller_lib.dir/src/communication/seat_service_client.cpp.s

# Object files for target body_controller_lib
body_controller_lib_OBJECTS = \
"CMakeFiles/body_controller_lib.dir/src/communication/someip_client.cpp.o" \
"CMakeFiles/body_controller_lib.dir/src/communication/door_service_client.cpp.o" \
"CMakeFiles/body_controller_lib.dir/src/communication/window_service_client.cpp.o" \
"CMakeFiles/body_controller_lib.dir/src/communication/light_service_client.cpp.o" \
"CMakeFiles/body_controller_lib.dir/src/communication/seat_service_client.cpp.o"

# External object files for target body_controller_lib
body_controller_lib_EXTERNAL_OBJECTS =

lib/libbody_controller_lib.a: CMakeFiles/body_controller_lib.dir/src/communication/someip_client.cpp.o
lib/libbody_controller_lib.a: CMakeFiles/body_controller_lib.dir/src/communication/door_service_client.cpp.o
lib/libbody_controller_lib.a: CMakeFiles/body_controller_lib.dir/src/communication/window_service_client.cpp.o
lib/libbody_controller_lib.a: CMakeFiles/body_controller_lib.dir/src/communication/light_service_client.cpp.o
lib/libbody_controller_lib.a: CMakeFiles/body_controller_lib.dir/src/communication/seat_service_client.cpp.o
lib/libbody_controller_lib.a: CMakeFiles/body_controller_lib.dir/build.make
lib/libbody_controller_lib.a: CMakeFiles/body_controller_lib.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Linking CXX static library lib/libbody_controller_lib.a"
	$(CMAKE_COMMAND) -P CMakeFiles/body_controller_lib.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/body_controller_lib.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/body_controller_lib.dir/build: lib/libbody_controller_lib.a
.PHONY : CMakeFiles/body_controller_lib.dir/build

CMakeFiles/body_controller_lib.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/body_controller_lib.dir/cmake_clean.cmake
.PHONY : CMakeFiles/body_controller_lib.dir/clean

CMakeFiles/body_controller_lib.dir/depend:
	cd /mnt/c/Users/<USER>/Desktop/car/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /mnt/c/Users/<USER>/Desktop/car /mnt/c/Users/<USER>/Desktop/car /mnt/c/Users/<USER>/Desktop/car/build /mnt/c/Users/<USER>/Desktop/car/build /mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles/body_controller_lib.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/body_controller_lib.dir/depend


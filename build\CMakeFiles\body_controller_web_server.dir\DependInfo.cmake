
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/mnt/c/Users/<USER>/Desktop/car/src/main_web_server.cpp" "CMakeFiles/body_controller_web_server.dir/src/main_web_server.cpp.o" "gcc" "CMakeFiles/body_controller_web_server.dir/src/main_web_server.cpp.o.d"
  "/mnt/c/Users/<USER>/Desktop/car/src/web_api/api_handlers.cpp" "CMakeFiles/body_controller_web_server.dir/src/web_api/api_handlers.cpp.o" "gcc" "CMakeFiles/body_controller_web_server.dir/src/web_api/api_handlers.cpp.o.d"
  "/mnt/c/Users/<USER>/Desktop/car/src/web_api/http_server.cpp" "CMakeFiles/body_controller_web_server.dir/src/web_api/http_server.cpp.o" "gcc" "CMakeFiles/body_controller_web_server.dir/src/web_api/http_server.cpp.o.d"
  "/mnt/c/Users/<USER>/Desktop/car/src/web_api/json_converter.cpp" "CMakeFiles/body_controller_web_server.dir/src/web_api/json_converter.cpp.o" "gcc" "CMakeFiles/body_controller_web_server.dir/src/web_api/json_converter.cpp.o.d"
  "/mnt/c/Users/<USER>/Desktop/car/src/web_api/websocket_server.cpp" "CMakeFiles/body_controller_web_server.dir/src/web_api/websocket_server.cpp.o" "gcc" "CMakeFiles/body_controller_web_server.dir/src/web_api/websocket_server.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")

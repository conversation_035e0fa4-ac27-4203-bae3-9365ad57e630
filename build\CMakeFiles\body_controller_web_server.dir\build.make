# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/c/Users/<USER>/Desktop/car

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/c/Users/<USER>/Desktop/car/build

# Include any dependencies generated for this target.
include CMakeFiles/body_controller_web_server.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/body_controller_web_server.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/body_controller_web_server.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/body_controller_web_server.dir/flags.make

CMakeFiles/body_controller_web_server.dir/src/main_web_server.cpp.o: CMakeFiles/body_controller_web_server.dir/flags.make
CMakeFiles/body_controller_web_server.dir/src/main_web_server.cpp.o: /mnt/c/Users/<USER>/Desktop/car/src/main_web_server.cpp
CMakeFiles/body_controller_web_server.dir/src/main_web_server.cpp.o: CMakeFiles/body_controller_web_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/body_controller_web_server.dir/src/main_web_server.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/body_controller_web_server.dir/src/main_web_server.cpp.o -MF CMakeFiles/body_controller_web_server.dir/src/main_web_server.cpp.o.d -o CMakeFiles/body_controller_web_server.dir/src/main_web_server.cpp.o -c /mnt/c/Users/<USER>/Desktop/car/src/main_web_server.cpp

CMakeFiles/body_controller_web_server.dir/src/main_web_server.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/body_controller_web_server.dir/src/main_web_server.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/c/Users/<USER>/Desktop/car/src/main_web_server.cpp > CMakeFiles/body_controller_web_server.dir/src/main_web_server.cpp.i

CMakeFiles/body_controller_web_server.dir/src/main_web_server.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/body_controller_web_server.dir/src/main_web_server.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/c/Users/<USER>/Desktop/car/src/main_web_server.cpp -o CMakeFiles/body_controller_web_server.dir/src/main_web_server.cpp.s

CMakeFiles/body_controller_web_server.dir/src/web_api/http_server.cpp.o: CMakeFiles/body_controller_web_server.dir/flags.make
CMakeFiles/body_controller_web_server.dir/src/web_api/http_server.cpp.o: /mnt/c/Users/<USER>/Desktop/car/src/web_api/http_server.cpp
CMakeFiles/body_controller_web_server.dir/src/web_api/http_server.cpp.o: CMakeFiles/body_controller_web_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/body_controller_web_server.dir/src/web_api/http_server.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/body_controller_web_server.dir/src/web_api/http_server.cpp.o -MF CMakeFiles/body_controller_web_server.dir/src/web_api/http_server.cpp.o.d -o CMakeFiles/body_controller_web_server.dir/src/web_api/http_server.cpp.o -c /mnt/c/Users/<USER>/Desktop/car/src/web_api/http_server.cpp

CMakeFiles/body_controller_web_server.dir/src/web_api/http_server.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/body_controller_web_server.dir/src/web_api/http_server.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/c/Users/<USER>/Desktop/car/src/web_api/http_server.cpp > CMakeFiles/body_controller_web_server.dir/src/web_api/http_server.cpp.i

CMakeFiles/body_controller_web_server.dir/src/web_api/http_server.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/body_controller_web_server.dir/src/web_api/http_server.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/c/Users/<USER>/Desktop/car/src/web_api/http_server.cpp -o CMakeFiles/body_controller_web_server.dir/src/web_api/http_server.cpp.s

CMakeFiles/body_controller_web_server.dir/src/web_api/websocket_server.cpp.o: CMakeFiles/body_controller_web_server.dir/flags.make
CMakeFiles/body_controller_web_server.dir/src/web_api/websocket_server.cpp.o: /mnt/c/Users/<USER>/Desktop/car/src/web_api/websocket_server.cpp
CMakeFiles/body_controller_web_server.dir/src/web_api/websocket_server.cpp.o: CMakeFiles/body_controller_web_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/body_controller_web_server.dir/src/web_api/websocket_server.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/body_controller_web_server.dir/src/web_api/websocket_server.cpp.o -MF CMakeFiles/body_controller_web_server.dir/src/web_api/websocket_server.cpp.o.d -o CMakeFiles/body_controller_web_server.dir/src/web_api/websocket_server.cpp.o -c /mnt/c/Users/<USER>/Desktop/car/src/web_api/websocket_server.cpp

CMakeFiles/body_controller_web_server.dir/src/web_api/websocket_server.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/body_controller_web_server.dir/src/web_api/websocket_server.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/c/Users/<USER>/Desktop/car/src/web_api/websocket_server.cpp > CMakeFiles/body_controller_web_server.dir/src/web_api/websocket_server.cpp.i

CMakeFiles/body_controller_web_server.dir/src/web_api/websocket_server.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/body_controller_web_server.dir/src/web_api/websocket_server.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/c/Users/<USER>/Desktop/car/src/web_api/websocket_server.cpp -o CMakeFiles/body_controller_web_server.dir/src/web_api/websocket_server.cpp.s

CMakeFiles/body_controller_web_server.dir/src/web_api/api_handlers.cpp.o: CMakeFiles/body_controller_web_server.dir/flags.make
CMakeFiles/body_controller_web_server.dir/src/web_api/api_handlers.cpp.o: /mnt/c/Users/<USER>/Desktop/car/src/web_api/api_handlers.cpp
CMakeFiles/body_controller_web_server.dir/src/web_api/api_handlers.cpp.o: CMakeFiles/body_controller_web_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/body_controller_web_server.dir/src/web_api/api_handlers.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/body_controller_web_server.dir/src/web_api/api_handlers.cpp.o -MF CMakeFiles/body_controller_web_server.dir/src/web_api/api_handlers.cpp.o.d -o CMakeFiles/body_controller_web_server.dir/src/web_api/api_handlers.cpp.o -c /mnt/c/Users/<USER>/Desktop/car/src/web_api/api_handlers.cpp

CMakeFiles/body_controller_web_server.dir/src/web_api/api_handlers.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/body_controller_web_server.dir/src/web_api/api_handlers.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/c/Users/<USER>/Desktop/car/src/web_api/api_handlers.cpp > CMakeFiles/body_controller_web_server.dir/src/web_api/api_handlers.cpp.i

CMakeFiles/body_controller_web_server.dir/src/web_api/api_handlers.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/body_controller_web_server.dir/src/web_api/api_handlers.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/c/Users/<USER>/Desktop/car/src/web_api/api_handlers.cpp -o CMakeFiles/body_controller_web_server.dir/src/web_api/api_handlers.cpp.s

CMakeFiles/body_controller_web_server.dir/src/web_api/json_converter.cpp.o: CMakeFiles/body_controller_web_server.dir/flags.make
CMakeFiles/body_controller_web_server.dir/src/web_api/json_converter.cpp.o: /mnt/c/Users/<USER>/Desktop/car/src/web_api/json_converter.cpp
CMakeFiles/body_controller_web_server.dir/src/web_api/json_converter.cpp.o: CMakeFiles/body_controller_web_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/body_controller_web_server.dir/src/web_api/json_converter.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/body_controller_web_server.dir/src/web_api/json_converter.cpp.o -MF CMakeFiles/body_controller_web_server.dir/src/web_api/json_converter.cpp.o.d -o CMakeFiles/body_controller_web_server.dir/src/web_api/json_converter.cpp.o -c /mnt/c/Users/<USER>/Desktop/car/src/web_api/json_converter.cpp

CMakeFiles/body_controller_web_server.dir/src/web_api/json_converter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/body_controller_web_server.dir/src/web_api/json_converter.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/c/Users/<USER>/Desktop/car/src/web_api/json_converter.cpp > CMakeFiles/body_controller_web_server.dir/src/web_api/json_converter.cpp.i

CMakeFiles/body_controller_web_server.dir/src/web_api/json_converter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/body_controller_web_server.dir/src/web_api/json_converter.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/c/Users/<USER>/Desktop/car/src/web_api/json_converter.cpp -o CMakeFiles/body_controller_web_server.dir/src/web_api/json_converter.cpp.s

# Object files for target body_controller_web_server
body_controller_web_server_OBJECTS = \
"CMakeFiles/body_controller_web_server.dir/src/main_web_server.cpp.o" \
"CMakeFiles/body_controller_web_server.dir/src/web_api/http_server.cpp.o" \
"CMakeFiles/body_controller_web_server.dir/src/web_api/websocket_server.cpp.o" \
"CMakeFiles/body_controller_web_server.dir/src/web_api/api_handlers.cpp.o" \
"CMakeFiles/body_controller_web_server.dir/src/web_api/json_converter.cpp.o"

# External object files for target body_controller_web_server
body_controller_web_server_EXTERNAL_OBJECTS =

bin/body_controller_web_server: CMakeFiles/body_controller_web_server.dir/src/main_web_server.cpp.o
bin/body_controller_web_server: CMakeFiles/body_controller_web_server.dir/src/web_api/http_server.cpp.o
bin/body_controller_web_server: CMakeFiles/body_controller_web_server.dir/src/web_api/websocket_server.cpp.o
bin/body_controller_web_server: CMakeFiles/body_controller_web_server.dir/src/web_api/api_handlers.cpp.o
bin/body_controller_web_server: CMakeFiles/body_controller_web_server.dir/src/web_api/json_converter.cpp.o
bin/body_controller_web_server: CMakeFiles/body_controller_web_server.dir/build.make
bin/body_controller_web_server: lib/libbody_controller_lib.a
bin/body_controller_web_server: /usr/local/lib/libvsomeip3.so.3.5.6
bin/body_controller_web_server: /usr/lib/x86_64-linux-gnu/libboost_system.so.1.83.0
bin/body_controller_web_server: /usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.83.0
bin/body_controller_web_server: /usr/lib/x86_64-linux-gnu/libboost_thread.so.1.83.0
bin/body_controller_web_server: /usr/lib/x86_64-linux-gnu/libboost_atomic.so.1.83.0
bin/body_controller_web_server: /usr/lib/x86_64-linux-gnu/libz.so
bin/body_controller_web_server: CMakeFiles/body_controller_web_server.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Linking CXX executable bin/body_controller_web_server"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/body_controller_web_server.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/body_controller_web_server.dir/build: bin/body_controller_web_server
.PHONY : CMakeFiles/body_controller_web_server.dir/build

CMakeFiles/body_controller_web_server.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/body_controller_web_server.dir/cmake_clean.cmake
.PHONY : CMakeFiles/body_controller_web_server.dir/clean

CMakeFiles/body_controller_web_server.dir/depend:
	cd /mnt/c/Users/<USER>/Desktop/car/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /mnt/c/Users/<USER>/Desktop/car /mnt/c/Users/<USER>/Desktop/car /mnt/c/Users/<USER>/Desktop/car/build /mnt/c/Users/<USER>/Desktop/car/build /mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles/body_controller_web_server.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/body_controller_web_server.dir/depend


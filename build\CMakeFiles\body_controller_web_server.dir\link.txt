/usr/bin/c++ -g -O0 -DDEBUG CMakeFiles/body_controller_web_server.dir/src/main_web_server.cpp.o CMakeFiles/body_controller_web_server.dir/src/web_api/http_server.cpp.o CMakeFiles/body_controller_web_server.dir/src/web_api/websocket_server.cpp.o CMakeFiles/body_controller_web_server.dir/src/web_api/api_handlers.cpp.o CMakeFiles/body_controller_web_server.dir/src/web_api/json_converter.cpp.o -o bin/body_controller_web_server  -Wl,-rpath,/usr/local/lib: lib/libbody_controller_lib.a /usr/local/lib/libvsomeip3.so.3.5.6 -lpthread -ldl -lrt /usr/lib/x86_64-linux-gnu/libboost_system.so.1.83.0 /usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.83.0 /usr/lib/x86_64-linux-gnu/libboost_thread.so.1.83.0 /usr/lib/x86_64-linux-gnu/libboost_atomic.so.1.83.0 /usr/lib/x86_64-linux-gnu/libz.so 

# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/c/Users/<USER>/Desktop/car

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/c/Users/<USER>/Desktop/car/build

# Include any dependencies generated for this target.
include CMakeFiles/test_light_client.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/test_light_client.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/test_light_client.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_light_client.dir/flags.make

CMakeFiles/test_light_client.dir/src/test_light_client.cpp.o: CMakeFiles/test_light_client.dir/flags.make
CMakeFiles/test_light_client.dir/src/test_light_client.cpp.o: /mnt/c/Users/<USER>/Desktop/car/src/test_light_client.cpp
CMakeFiles/test_light_client.dir/src/test_light_client.cpp.o: CMakeFiles/test_light_client.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/test_light_client.dir/src/test_light_client.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_light_client.dir/src/test_light_client.cpp.o -MF CMakeFiles/test_light_client.dir/src/test_light_client.cpp.o.d -o CMakeFiles/test_light_client.dir/src/test_light_client.cpp.o -c /mnt/c/Users/<USER>/Desktop/car/src/test_light_client.cpp

CMakeFiles/test_light_client.dir/src/test_light_client.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/test_light_client.dir/src/test_light_client.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/c/Users/<USER>/Desktop/car/src/test_light_client.cpp > CMakeFiles/test_light_client.dir/src/test_light_client.cpp.i

CMakeFiles/test_light_client.dir/src/test_light_client.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/test_light_client.dir/src/test_light_client.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/c/Users/<USER>/Desktop/car/src/test_light_client.cpp -o CMakeFiles/test_light_client.dir/src/test_light_client.cpp.s

# Object files for target test_light_client
test_light_client_OBJECTS = \
"CMakeFiles/test_light_client.dir/src/test_light_client.cpp.o"

# External object files for target test_light_client
test_light_client_EXTERNAL_OBJECTS =

bin/test_light_client: CMakeFiles/test_light_client.dir/src/test_light_client.cpp.o
bin/test_light_client: CMakeFiles/test_light_client.dir/build.make
bin/test_light_client: lib/libbody_controller_lib.a
bin/test_light_client: /usr/local/lib/libvsomeip3.so.3.5.6
bin/test_light_client: /usr/lib/x86_64-linux-gnu/libboost_system.so.1.83.0
bin/test_light_client: /usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.83.0
bin/test_light_client: /usr/lib/x86_64-linux-gnu/libboost_thread.so.1.83.0
bin/test_light_client: /usr/lib/x86_64-linux-gnu/libboost_atomic.so.1.83.0
bin/test_light_client: CMakeFiles/test_light_client.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable bin/test_light_client"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/test_light_client.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/test_light_client.dir/build: bin/test_light_client
.PHONY : CMakeFiles/test_light_client.dir/build

CMakeFiles/test_light_client.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/test_light_client.dir/cmake_clean.cmake
.PHONY : CMakeFiles/test_light_client.dir/clean

CMakeFiles/test_light_client.dir/depend:
	cd /mnt/c/Users/<USER>/Desktop/car/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /mnt/c/Users/<USER>/Desktop/car /mnt/c/Users/<USER>/Desktop/car /mnt/c/Users/<USER>/Desktop/car/build /mnt/c/Users/<USER>/Desktop/car/build /mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles/test_light_client.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/test_light_client.dir/depend


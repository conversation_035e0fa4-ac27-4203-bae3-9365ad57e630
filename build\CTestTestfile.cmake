# CMake generated Testfile for 
# Source directory: /mnt/c/Users/<USER>/Desktop/car
# Build directory: /mnt/c/Users/<USER>/Desktop/car/build
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(test_door_client_help "/mnt/c/Users/<USER>/Desktop/car/build/bin/test_door_client" "--help")
set_tests_properties(test_door_client_help PROPERTIES  WORKING_DIRECTORY "/mnt/c/Users/<USER>/Desktop/car/build" _BACKTRACE_TRIPLES "/mnt/c/Users/<USER>/Desktop/car/CMakeLists.txt;270;add_test;/mnt/c/Users/<USER>/Desktop/car/CMakeLists.txt;0;")
add_test(test_window_client_help "/mnt/c/Users/<USER>/Desktop/car/build/bin/test_window_client" "--help")
set_tests_properties(test_window_client_help PROPERTIES  WORKING_DIRECTORY "/mnt/c/Users/<USER>/Desktop/car/build" _BACKTRACE_TRIPLES "/mnt/c/Users/<USER>/Desktop/car/CMakeLists.txt;274;add_test;/mnt/c/Users/<USER>/Desktop/car/CMakeLists.txt;0;")
add_test(test_light_client_help "/mnt/c/Users/<USER>/Desktop/car/build/bin/test_light_client" "--help")
set_tests_properties(test_light_client_help PROPERTIES  WORKING_DIRECTORY "/mnt/c/Users/<USER>/Desktop/car/build" _BACKTRACE_TRIPLES "/mnt/c/Users/<USER>/Desktop/car/CMakeLists.txt;278;add_test;/mnt/c/Users/<USER>/Desktop/car/CMakeLists.txt;0;")
add_test(test_seat_client_help "/mnt/c/Users/<USER>/Desktop/car/build/bin/test_seat_client" "--help")
set_tests_properties(test_seat_client_help PROPERTIES  WORKING_DIRECTORY "/mnt/c/Users/<USER>/Desktop/car/build" _BACKTRACE_TRIPLES "/mnt/c/Users/<USER>/Desktop/car/CMakeLists.txt;282;add_test;/mnt/c/Users/<USER>/Desktop/car/CMakeLists.txt;0;")
subdirs("_deps/httplib-build")
subdirs("_deps/json-build")

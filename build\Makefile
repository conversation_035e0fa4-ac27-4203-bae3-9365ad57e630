# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/c/Users/<USER>/Desktop/car

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/c/Users/<USER>/Desktop/car/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles /mnt/c/Users/<USER>/Desktop/car/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/c/Users/<USER>/Desktop/car/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named body_controller_lib

# Build rule for target.
body_controller_lib: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 body_controller_lib
.PHONY : body_controller_lib

# fast build rule for target.
body_controller_lib/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_lib.dir/build.make CMakeFiles/body_controller_lib.dir/build
.PHONY : body_controller_lib/fast

#=============================================================================
# Target rules for targets named test_door_client

# Build rule for target.
test_door_client: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_door_client
.PHONY : test_door_client

# fast build rule for target.
test_door_client/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_door_client.dir/build.make CMakeFiles/test_door_client.dir/build
.PHONY : test_door_client/fast

#=============================================================================
# Target rules for targets named test_window_client

# Build rule for target.
test_window_client: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_window_client
.PHONY : test_window_client

# fast build rule for target.
test_window_client/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_window_client.dir/build.make CMakeFiles/test_window_client.dir/build
.PHONY : test_window_client/fast

#=============================================================================
# Target rules for targets named test_light_client

# Build rule for target.
test_light_client: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_light_client
.PHONY : test_light_client

# fast build rule for target.
test_light_client/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_light_client.dir/build.make CMakeFiles/test_light_client.dir/build
.PHONY : test_light_client/fast

#=============================================================================
# Target rules for targets named test_seat_client

# Build rule for target.
test_seat_client: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_seat_client
.PHONY : test_seat_client

# fast build rule for target.
test_seat_client/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_seat_client.dir/build.make CMakeFiles/test_seat_client.dir/build
.PHONY : test_seat_client/fast

#=============================================================================
# Target rules for targets named body_controller_web_server

# Build rule for target.
body_controller_web_server: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 body_controller_web_server
.PHONY : body_controller_web_server

# fast build rule for target.
body_controller_web_server/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_web_server.dir/build.make CMakeFiles/body_controller_web_server.dir/build
.PHONY : body_controller_web_server/fast

src/communication/door_service_client.o: src/communication/door_service_client.cpp.o
.PHONY : src/communication/door_service_client.o

# target to build an object file
src/communication/door_service_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_lib.dir/build.make CMakeFiles/body_controller_lib.dir/src/communication/door_service_client.cpp.o
.PHONY : src/communication/door_service_client.cpp.o

src/communication/door_service_client.i: src/communication/door_service_client.cpp.i
.PHONY : src/communication/door_service_client.i

# target to preprocess a source file
src/communication/door_service_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_lib.dir/build.make CMakeFiles/body_controller_lib.dir/src/communication/door_service_client.cpp.i
.PHONY : src/communication/door_service_client.cpp.i

src/communication/door_service_client.s: src/communication/door_service_client.cpp.s
.PHONY : src/communication/door_service_client.s

# target to generate assembly for a file
src/communication/door_service_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_lib.dir/build.make CMakeFiles/body_controller_lib.dir/src/communication/door_service_client.cpp.s
.PHONY : src/communication/door_service_client.cpp.s

src/communication/light_service_client.o: src/communication/light_service_client.cpp.o
.PHONY : src/communication/light_service_client.o

# target to build an object file
src/communication/light_service_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_lib.dir/build.make CMakeFiles/body_controller_lib.dir/src/communication/light_service_client.cpp.o
.PHONY : src/communication/light_service_client.cpp.o

src/communication/light_service_client.i: src/communication/light_service_client.cpp.i
.PHONY : src/communication/light_service_client.i

# target to preprocess a source file
src/communication/light_service_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_lib.dir/build.make CMakeFiles/body_controller_lib.dir/src/communication/light_service_client.cpp.i
.PHONY : src/communication/light_service_client.cpp.i

src/communication/light_service_client.s: src/communication/light_service_client.cpp.s
.PHONY : src/communication/light_service_client.s

# target to generate assembly for a file
src/communication/light_service_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_lib.dir/build.make CMakeFiles/body_controller_lib.dir/src/communication/light_service_client.cpp.s
.PHONY : src/communication/light_service_client.cpp.s

src/communication/seat_service_client.o: src/communication/seat_service_client.cpp.o
.PHONY : src/communication/seat_service_client.o

# target to build an object file
src/communication/seat_service_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_lib.dir/build.make CMakeFiles/body_controller_lib.dir/src/communication/seat_service_client.cpp.o
.PHONY : src/communication/seat_service_client.cpp.o

src/communication/seat_service_client.i: src/communication/seat_service_client.cpp.i
.PHONY : src/communication/seat_service_client.i

# target to preprocess a source file
src/communication/seat_service_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_lib.dir/build.make CMakeFiles/body_controller_lib.dir/src/communication/seat_service_client.cpp.i
.PHONY : src/communication/seat_service_client.cpp.i

src/communication/seat_service_client.s: src/communication/seat_service_client.cpp.s
.PHONY : src/communication/seat_service_client.s

# target to generate assembly for a file
src/communication/seat_service_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_lib.dir/build.make CMakeFiles/body_controller_lib.dir/src/communication/seat_service_client.cpp.s
.PHONY : src/communication/seat_service_client.cpp.s

src/communication/someip_client.o: src/communication/someip_client.cpp.o
.PHONY : src/communication/someip_client.o

# target to build an object file
src/communication/someip_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_lib.dir/build.make CMakeFiles/body_controller_lib.dir/src/communication/someip_client.cpp.o
.PHONY : src/communication/someip_client.cpp.o

src/communication/someip_client.i: src/communication/someip_client.cpp.i
.PHONY : src/communication/someip_client.i

# target to preprocess a source file
src/communication/someip_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_lib.dir/build.make CMakeFiles/body_controller_lib.dir/src/communication/someip_client.cpp.i
.PHONY : src/communication/someip_client.cpp.i

src/communication/someip_client.s: src/communication/someip_client.cpp.s
.PHONY : src/communication/someip_client.s

# target to generate assembly for a file
src/communication/someip_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_lib.dir/build.make CMakeFiles/body_controller_lib.dir/src/communication/someip_client.cpp.s
.PHONY : src/communication/someip_client.cpp.s

src/communication/window_service_client.o: src/communication/window_service_client.cpp.o
.PHONY : src/communication/window_service_client.o

# target to build an object file
src/communication/window_service_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_lib.dir/build.make CMakeFiles/body_controller_lib.dir/src/communication/window_service_client.cpp.o
.PHONY : src/communication/window_service_client.cpp.o

src/communication/window_service_client.i: src/communication/window_service_client.cpp.i
.PHONY : src/communication/window_service_client.i

# target to preprocess a source file
src/communication/window_service_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_lib.dir/build.make CMakeFiles/body_controller_lib.dir/src/communication/window_service_client.cpp.i
.PHONY : src/communication/window_service_client.cpp.i

src/communication/window_service_client.s: src/communication/window_service_client.cpp.s
.PHONY : src/communication/window_service_client.s

# target to generate assembly for a file
src/communication/window_service_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_lib.dir/build.make CMakeFiles/body_controller_lib.dir/src/communication/window_service_client.cpp.s
.PHONY : src/communication/window_service_client.cpp.s

src/main_web_server.o: src/main_web_server.cpp.o
.PHONY : src/main_web_server.o

# target to build an object file
src/main_web_server.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_web_server.dir/build.make CMakeFiles/body_controller_web_server.dir/src/main_web_server.cpp.o
.PHONY : src/main_web_server.cpp.o

src/main_web_server.i: src/main_web_server.cpp.i
.PHONY : src/main_web_server.i

# target to preprocess a source file
src/main_web_server.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_web_server.dir/build.make CMakeFiles/body_controller_web_server.dir/src/main_web_server.cpp.i
.PHONY : src/main_web_server.cpp.i

src/main_web_server.s: src/main_web_server.cpp.s
.PHONY : src/main_web_server.s

# target to generate assembly for a file
src/main_web_server.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_web_server.dir/build.make CMakeFiles/body_controller_web_server.dir/src/main_web_server.cpp.s
.PHONY : src/main_web_server.cpp.s

src/test_door_client.o: src/test_door_client.cpp.o
.PHONY : src/test_door_client.o

# target to build an object file
src/test_door_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_door_client.dir/build.make CMakeFiles/test_door_client.dir/src/test_door_client.cpp.o
.PHONY : src/test_door_client.cpp.o

src/test_door_client.i: src/test_door_client.cpp.i
.PHONY : src/test_door_client.i

# target to preprocess a source file
src/test_door_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_door_client.dir/build.make CMakeFiles/test_door_client.dir/src/test_door_client.cpp.i
.PHONY : src/test_door_client.cpp.i

src/test_door_client.s: src/test_door_client.cpp.s
.PHONY : src/test_door_client.s

# target to generate assembly for a file
src/test_door_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_door_client.dir/build.make CMakeFiles/test_door_client.dir/src/test_door_client.cpp.s
.PHONY : src/test_door_client.cpp.s

src/test_light_client.o: src/test_light_client.cpp.o
.PHONY : src/test_light_client.o

# target to build an object file
src/test_light_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_light_client.dir/build.make CMakeFiles/test_light_client.dir/src/test_light_client.cpp.o
.PHONY : src/test_light_client.cpp.o

src/test_light_client.i: src/test_light_client.cpp.i
.PHONY : src/test_light_client.i

# target to preprocess a source file
src/test_light_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_light_client.dir/build.make CMakeFiles/test_light_client.dir/src/test_light_client.cpp.i
.PHONY : src/test_light_client.cpp.i

src/test_light_client.s: src/test_light_client.cpp.s
.PHONY : src/test_light_client.s

# target to generate assembly for a file
src/test_light_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_light_client.dir/build.make CMakeFiles/test_light_client.dir/src/test_light_client.cpp.s
.PHONY : src/test_light_client.cpp.s

src/test_seat_client.o: src/test_seat_client.cpp.o
.PHONY : src/test_seat_client.o

# target to build an object file
src/test_seat_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_seat_client.dir/build.make CMakeFiles/test_seat_client.dir/src/test_seat_client.cpp.o
.PHONY : src/test_seat_client.cpp.o

src/test_seat_client.i: src/test_seat_client.cpp.i
.PHONY : src/test_seat_client.i

# target to preprocess a source file
src/test_seat_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_seat_client.dir/build.make CMakeFiles/test_seat_client.dir/src/test_seat_client.cpp.i
.PHONY : src/test_seat_client.cpp.i

src/test_seat_client.s: src/test_seat_client.cpp.s
.PHONY : src/test_seat_client.s

# target to generate assembly for a file
src/test_seat_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_seat_client.dir/build.make CMakeFiles/test_seat_client.dir/src/test_seat_client.cpp.s
.PHONY : src/test_seat_client.cpp.s

src/test_window_client.o: src/test_window_client.cpp.o
.PHONY : src/test_window_client.o

# target to build an object file
src/test_window_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_window_client.dir/build.make CMakeFiles/test_window_client.dir/src/test_window_client.cpp.o
.PHONY : src/test_window_client.cpp.o

src/test_window_client.i: src/test_window_client.cpp.i
.PHONY : src/test_window_client.i

# target to preprocess a source file
src/test_window_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_window_client.dir/build.make CMakeFiles/test_window_client.dir/src/test_window_client.cpp.i
.PHONY : src/test_window_client.cpp.i

src/test_window_client.s: src/test_window_client.cpp.s
.PHONY : src/test_window_client.s

# target to generate assembly for a file
src/test_window_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_window_client.dir/build.make CMakeFiles/test_window_client.dir/src/test_window_client.cpp.s
.PHONY : src/test_window_client.cpp.s

src/web_api/api_handlers.o: src/web_api/api_handlers.cpp.o
.PHONY : src/web_api/api_handlers.o

# target to build an object file
src/web_api/api_handlers.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_web_server.dir/build.make CMakeFiles/body_controller_web_server.dir/src/web_api/api_handlers.cpp.o
.PHONY : src/web_api/api_handlers.cpp.o

src/web_api/api_handlers.i: src/web_api/api_handlers.cpp.i
.PHONY : src/web_api/api_handlers.i

# target to preprocess a source file
src/web_api/api_handlers.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_web_server.dir/build.make CMakeFiles/body_controller_web_server.dir/src/web_api/api_handlers.cpp.i
.PHONY : src/web_api/api_handlers.cpp.i

src/web_api/api_handlers.s: src/web_api/api_handlers.cpp.s
.PHONY : src/web_api/api_handlers.s

# target to generate assembly for a file
src/web_api/api_handlers.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_web_server.dir/build.make CMakeFiles/body_controller_web_server.dir/src/web_api/api_handlers.cpp.s
.PHONY : src/web_api/api_handlers.cpp.s

src/web_api/http_server.o: src/web_api/http_server.cpp.o
.PHONY : src/web_api/http_server.o

# target to build an object file
src/web_api/http_server.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_web_server.dir/build.make CMakeFiles/body_controller_web_server.dir/src/web_api/http_server.cpp.o
.PHONY : src/web_api/http_server.cpp.o

src/web_api/http_server.i: src/web_api/http_server.cpp.i
.PHONY : src/web_api/http_server.i

# target to preprocess a source file
src/web_api/http_server.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_web_server.dir/build.make CMakeFiles/body_controller_web_server.dir/src/web_api/http_server.cpp.i
.PHONY : src/web_api/http_server.cpp.i

src/web_api/http_server.s: src/web_api/http_server.cpp.s
.PHONY : src/web_api/http_server.s

# target to generate assembly for a file
src/web_api/http_server.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_web_server.dir/build.make CMakeFiles/body_controller_web_server.dir/src/web_api/http_server.cpp.s
.PHONY : src/web_api/http_server.cpp.s

src/web_api/json_converter.o: src/web_api/json_converter.cpp.o
.PHONY : src/web_api/json_converter.o

# target to build an object file
src/web_api/json_converter.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_web_server.dir/build.make CMakeFiles/body_controller_web_server.dir/src/web_api/json_converter.cpp.o
.PHONY : src/web_api/json_converter.cpp.o

src/web_api/json_converter.i: src/web_api/json_converter.cpp.i
.PHONY : src/web_api/json_converter.i

# target to preprocess a source file
src/web_api/json_converter.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_web_server.dir/build.make CMakeFiles/body_controller_web_server.dir/src/web_api/json_converter.cpp.i
.PHONY : src/web_api/json_converter.cpp.i

src/web_api/json_converter.s: src/web_api/json_converter.cpp.s
.PHONY : src/web_api/json_converter.s

# target to generate assembly for a file
src/web_api/json_converter.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_web_server.dir/build.make CMakeFiles/body_controller_web_server.dir/src/web_api/json_converter.cpp.s
.PHONY : src/web_api/json_converter.cpp.s

src/web_api/websocket_server.o: src/web_api/websocket_server.cpp.o
.PHONY : src/web_api/websocket_server.o

# target to build an object file
src/web_api/websocket_server.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_web_server.dir/build.make CMakeFiles/body_controller_web_server.dir/src/web_api/websocket_server.cpp.o
.PHONY : src/web_api/websocket_server.cpp.o

src/web_api/websocket_server.i: src/web_api/websocket_server.cpp.i
.PHONY : src/web_api/websocket_server.i

# target to preprocess a source file
src/web_api/websocket_server.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_web_server.dir/build.make CMakeFiles/body_controller_web_server.dir/src/web_api/websocket_server.cpp.i
.PHONY : src/web_api/websocket_server.cpp.i

src/web_api/websocket_server.s: src/web_api/websocket_server.cpp.s
.PHONY : src/web_api/websocket_server.s

# target to generate assembly for a file
src/web_api/websocket_server.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/body_controller_web_server.dir/build.make CMakeFiles/body_controller_web_server.dir/src/web_api/websocket_server.cpp.s
.PHONY : src/web_api/websocket_server.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... body_controller_lib"
	@echo "... body_controller_web_server"
	@echo "... test_door_client"
	@echo "... test_light_client"
	@echo "... test_seat_client"
	@echo "... test_window_client"
	@echo "... src/communication/door_service_client.o"
	@echo "... src/communication/door_service_client.i"
	@echo "... src/communication/door_service_client.s"
	@echo "... src/communication/light_service_client.o"
	@echo "... src/communication/light_service_client.i"
	@echo "... src/communication/light_service_client.s"
	@echo "... src/communication/seat_service_client.o"
	@echo "... src/communication/seat_service_client.i"
	@echo "... src/communication/seat_service_client.s"
	@echo "... src/communication/someip_client.o"
	@echo "... src/communication/someip_client.i"
	@echo "... src/communication/someip_client.s"
	@echo "... src/communication/window_service_client.o"
	@echo "... src/communication/window_service_client.i"
	@echo "... src/communication/window_service_client.s"
	@echo "... src/main_web_server.o"
	@echo "... src/main_web_server.i"
	@echo "... src/main_web_server.s"
	@echo "... src/test_door_client.o"
	@echo "... src/test_door_client.i"
	@echo "... src/test_door_client.s"
	@echo "... src/test_light_client.o"
	@echo "... src/test_light_client.i"
	@echo "... src/test_light_client.s"
	@echo "... src/test_seat_client.o"
	@echo "... src/test_seat_client.i"
	@echo "... src/test_seat_client.s"
	@echo "... src/test_window_client.o"
	@echo "... src/test_window_client.i"
	@echo "... src/test_window_client.s"
	@echo "... src/web_api/api_handlers.o"
	@echo "... src/web_api/api_handlers.i"
	@echo "... src/web_api/api_handlers.s"
	@echo "... src/web_api/http_server.o"
	@echo "... src/web_api/http_server.i"
	@echo "... src/web_api/http_server.s"
	@echo "... src/web_api/json_converter.o"
	@echo "... src/web_api/json_converter.i"
	@echo "... src/web_api/json_converter.s"
	@echo "... src/web_api/websocket_server.o"
	@echo "... src/web_api/websocket_server.i"
	@echo "... src/web_api/websocket_server.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system


# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "CMakeFiles/3.28.3/CMakeSystem.cmake"
  "CMakeLists.txt"
  "httplib-populate-prefix/tmp/httplib-populate-mkdirs.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.28/Modules/ExternalProject.cmake"
  "/usr/share/cmake-3.28/Modules/ExternalProject/PatchInfo.txt.in"
  "/usr/share/cmake-3.28/Modules/ExternalProject/RepositoryInfo.txt.in"
  "/usr/share/cmake-3.28/Modules/ExternalProject/UpdateInfo.txt.in"
  "/usr/share/cmake-3.28/Modules/ExternalProject/cfgcmd.txt.in"
  "/usr/share/cmake-3.28/Modules/ExternalProject/gitclone.cmake.in"
  "/usr/share/cmake-3.28/Modules/ExternalProject/gitupdate.cmake.in"
  "/usr/share/cmake-3.28/Modules/ExternalProject/mkdirs.cmake.in"
  "/usr/share/cmake-3.28/Modules/ExternalProject/shared_internal_commands.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux-Initialize.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "httplib-populate-prefix/tmp/httplib-populate-mkdirs.cmake"
  "httplib-populate-prefix/tmp/httplib-populate-gitclone.cmake"
  "httplib-populate-prefix/src/httplib-populate-stamp/httplib-populate-gitinfo.txt"
  "httplib-populate-prefix/tmp/httplib-populate-gitupdate.cmake"
  "httplib-populate-prefix/src/httplib-populate-stamp/httplib-populate-update-info.txt"
  "httplib-populate-prefix/src/httplib-populate-stamp/httplib-populate-patch-info.txt"
  "httplib-populate-prefix/tmp/httplib-populate-cfgcmd.txt"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/httplib-populate.dir/DependInfo.cmake"
  )

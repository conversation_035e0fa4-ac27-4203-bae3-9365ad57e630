REQUIRES: lsan
RUN: not LLVMFuzzer-LeakTest -runs=100000 -detect_leaks=1 2>&1 | FileCheck %s --check-prefix=LEAK_DURING
LEAK_DURING: ERROR: LeakSanitizer: detected memory leaks
LEAK_DURING: Direct leak of 4 byte(s) in 1 object(s) allocated from:
LEAK_DURING: INFO: to ignore leaks on libFuzzer side use -detect_leaks=0
LEAK_DURING: Test unit written to ./leak-
LEAK_DURING-NOT: DONE
LEAK_DURING-NOT: Done

RUN: not LLVMFuzzer-LeakTest -runs=0 -detect_leaks=1 %S 2>&1 | FileCheck %s --check-prefix=LEAK_IN_CORPUS
LEAK_IN_CORPUS: ERROR: LeakSanitizer: detected memory leaks
LEAK_IN_CORPUS: INFO: a leak has been found in the initial corpus.

RUN: not LLVMFuzzer-LeakTest -runs=100000000 %S/hi.txt 2>&1 | FileCheck %s --check-prefix=MULTI_RUN_LEAK
MULTI_RUN_LEAK-NOT: pulse
MULTI_RUN_LEAK: LeakSanitizer: detected memory leaks

RUN: not LLVMFuzzer-LeakTest -runs=100000 -detect_leaks=0 2>&1 | FileCheck %s --check-prefix=LEAK_AFTER
RUN: not LLVMFuzzer-LeakTest -runs=100000                 2>&1 | FileCheck %s --check-prefix=LEAK_DURING
RUN: not LLVMFuzzer-ThreadedLeakTest -runs=100000 -detect_leaks=0 2>&1 | FileCheck %s --check-prefix=LEAK_AFTER
RUN: not LLVMFuzzer-ThreadedLeakTest -runs=100000                 2>&1 | FileCheck %s --check-prefix=LEAK_DURING
LEAK_AFTER: Done 100000 runs in
LEAK_AFTER: ERROR: LeakSanitizer: detected memory leaks

RUN: not LLVMFuzzer-LeakTest -runs=100000 -max_len=1 2>&1 | FileCheck %s --check-prefix=MAX_LEN_1
MAX_LEN_1: Test unit written to ./leak-7cf184f4c67ad58283ecb19349720b0cae756829

RUN: not LLVMFuzzer-LeakTimeoutTest -timeout=1 2>&1 | FileCheck %s --check-prefix=LEAK_TIMEOUT
LEAK_TIMEOUT: ERROR: libFuzzer: timeout after
LEAK_TIMEOUT-NOT: LeakSanitizer

RUN: LLVMFuzzer-AccumulateAllocationsTest -detect_leaks=1 -runs=100000 2>&1 | FileCheck %s --check-prefix=ACCUMULATE_ALLOCS
ACCUMULATE_ALLOCS: INFO: libFuzzer disabled leak detection after every mutation

RUN: LLVMFuzzer-LeakTest -error_exitcode=0

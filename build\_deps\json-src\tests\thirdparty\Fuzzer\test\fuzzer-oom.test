RUN: not LLVMFuzzer-OutOfMemoryTest -rss_limit_mb=300 2>&1 | FileCheck %s
CHECK: ERROR: libFuzzer: out-of-memory (used: {{.*}}; limit: 300Mb)
CHECK: Test unit written to ./oom-
SUMMARY: libFuzzer: out-of-memory

RUN: not LLVMFuzzer-OutOfMemorySingleLargeMallocTest 2>&1 | FileCheck %s --check-prefix=SINGLE_LARGE_MALLOC
SINGLE_LARGE_MALLOC: libFuzzer: out-of-memory (malloc(42{{.*}}))
SINGLE_LARGE_MALLOC: in LLVMFuzzerTestOneInput

# Check that -rss_limit_mb=0 means no limit.
RUN: LLVMFuzzer-AccumulateAllocationsTest -runs=1000 -rss_limit_mb=0

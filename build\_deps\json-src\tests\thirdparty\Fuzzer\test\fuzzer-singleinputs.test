RUN: not LLVMFuzzer-NullDerefTest %S/hi.txt 2>&1 | FileCheck %s --check-prefix=SingleInput
SingleInput-NOT: Test unit written to ./crash-

RUN: rm -rf  %tmp/SINGLE_INPUTS
RUN: mkdir -p  %tmp/SINGLE_INPUTS
RUN: echo aaa > %tmp/SINGLE_INPUTS/aaa
RUN: echo bbb > %tmp/SINGLE_INPUTS/bbb
RUN: LLVMFuzzer-SimpleTest            %tmp/SINGLE_INPUTS/aaa %tmp/SINGLE_INPUTS/bbb 2>&1 | FileCheck %s --check-prefix=SINGLE_INPUTS
RUN: LLVMFuzzer-SimpleTest -max_len=2 %tmp/SINGLE_INPUTS/aaa %tmp/SINGLE_INPUTS/bbb 2>&1 | FileCheck %s --check-prefix=SINGLE_INPUTS
RUN: rm -rf  %tmp/SINGLE_INPUTS
SINGLE_INPUTS: LLVMFuzzer-SimpleTest: Running 2 inputs 1 time(s) each.
SINGLE_INPUTS: aaa in
SINGLE_INPUTS: bbb in
SINGLE_INPUTS: NOTE: fuzzing was not performed, you have only
SINGLE_INPUTS: executed the target code on a fixed set of inputs.


{"unicast": "************", "netmask": "*************", "logging": {"level": "info", "console": true, "file": {"enable": true, "path": "/tmp/vsomeip.log"}, "dlt": false}, "applications": [{"name": "body_controller", "id": "0x1000"}], "services": [{"service": "0x1001", "instance": "0x1001", "unicast": "************", "reliable": {"port": "30501", "enable-magic-cookies": false}, "events": [{"event": "0x8001", "is_field": false, "is_reliable": true}], "eventgroups": [{"eventgroup": "0x0001", "events": ["0x8001"], "is_multicast": false, "threshold": 0}]}, {"service": "0x1002", "instance": "0x1002", "unicast": "************", "reliable": {"port": "30502", "enable-magic-cookies": false}, "events": [{"event": "0x8001", "is_field": false, "is_reliable": true}, {"event": "0x8002", "is_field": false, "is_reliable": true}], "eventgroups": [{"eventgroup": "0x0001", "events": ["0x8001", "0x8002"], "is_multicast": false, "threshold": 0}]}, {"service": "0x1003", "instance": "0x1003", "unicast": "************", "reliable": {"port": "30503", "enable-magic-cookies": false}, "events": [{"event": "0x8001", "is_field": false, "is_reliable": true}], "eventgroups": [{"eventgroup": "0x0001", "events": ["0x8001"], "is_multicast": false, "threshold": 0}]}, {"service": "0x1004", "instance": "0x1004", "unicast": "************", "reliable": {"port": "30504", "enable-magic-cookies": false}, "events": [{"event": "0x8001", "is_field": false, "is_reliable": true}, {"event": "0x8002", "is_field": false, "is_reliable": true}], "eventgroups": [{"eventgroup": "0x0001", "events": ["0x8001", "0x8002"], "is_multicast": false, "threshold": 0}]}], "clients": [{"service": "0x1001", "instance": "0x1001", "reliable": ["40001"]}, {"service": "0x1002", "instance": "0x1002", "reliable": ["40002"]}, {"service": "0x1003", "instance": "0x1003", "reliable": ["40003"]}, {"service": "0x1004", "instance": "0x1004", "reliable": ["40004"]}], "routing": "body_controller", "service-discovery": {"enable": true, "multicast": "224.244.224.245", "port": "30490", "protocol": "udp", "initial_delay_min": "10", "initial_delay_max": "100", "repetitions_base_delay": "200", "repetitions_max": "3", "ttl": "3", "cyclic_offer_delay": "2000", "request_response_delay": "1500"}, "payload-sizes": [{"unicast": "************", "ports": [{"port": "30501", "max-payload-size": "1400"}, {"port": "30502", "max-payload-size": "1400"}, {"port": "30503", "max-payload-size": "1400"}, {"port": "30504", "max-payload-size": "1400"}]}], "internal_services": {"first": "0x8100", "last": "0x81FF"}, "diagnosis": {"diagnosis_address": "0x10", "enable": false}, "shutdown_timeout": "5000"}
#!/bin/bash
export VSOMEIP_CONFIGURATION=/mnt/c/Users/<USER>/Desktop/car/build/config/vsomeip.json
export VSOMEIP_APPLICATION_NAME=web_server
echo "Starting Body Controller Web Server..."
echo "Configuration: $VSOMEIP_CONFIGURATION"
echo "Application: $VSOMEIP_APPLICATION_NAME"
echo "HTTP Server: http://localhost:8080"
echo "WebSocket: ws://localhost:8081"
echo "Press Ctrl+C to exit"
/mnt/c/Users/<USER>/Desktop/car/build/bin/body_controller_web_server --http-port 8080 --ws-port 8081

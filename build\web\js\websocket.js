/**
 * Body Controller WebSocket Client
 * 处理与后端WebSocket服务器的实时通信
 */

class BodyControllerWebSocket {
    constructor(url = 'ws://localhost:8081') {
        this.url = url;
        this.ws = null;
        this.eventHandlers = new Map();
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // 初始重连延迟1秒
        this.heartbeatInterval = 30000; // 心跳间隔30秒
        this.heartbeatTimer = null;
        this.isConnected = false;
        this.isReconnecting = false;
        
        // 绑定事件处理器
        this.onOpen = this.onOpen.bind(this);
        this.onMessage = this.onMessage.bind(this);
        this.onClose = this.onClose.bind(this);
        this.onError = this.onError.bind(this);
    }

    /**
     * 连接WebSocket服务器
     */
    connect() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            console.log('WebSocket already connected');
            return;
        }

        if (!window.WebSocket) {
            console.error('WebSocket not supported by this browser');
            this.emit('error', new Error('WebSocket not supported'));
            return;
        }

        try {
            console.log(`Connecting to WebSocket: ${this.url}`);
            this.ws = new WebSocket(this.url);
            
            this.ws.onopen = this.onOpen;
            this.ws.onmessage = this.onMessage;
            this.ws.onclose = this.onClose;
            this.ws.onerror = this.onError;
            
        } catch (error) {
            console.error('Failed to create WebSocket connection:', error);
            this.emit('error', error);
        }
    }

    /**
     * 断开WebSocket连接
     */
    disconnect() {
        this.isReconnecting = false;
        this.stopHeartbeat();
        
        if (this.ws) {
            this.ws.onopen = null;
            this.ws.onmessage = null;
            this.ws.onclose = null;
            this.ws.onerror = null;
            
            if (this.ws.readyState === WebSocket.OPEN) {
                this.ws.close(1000, 'Client disconnect');
            }
            this.ws = null;
        }
        
        this.isConnected = false;
        this.emit('disconnected');
    }

    /**
     * WebSocket连接打开事件
     */
    onOpen(event) {
        console.log('WebSocket connected');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.isReconnecting = false;
        
        this.startHeartbeat();
        this.emit('connected', event);
        
        // 订阅所有事件类型
        this.subscribeToAllEvents();
    }

    /**
     * WebSocket消息接收事件
     */
    onMessage(event) {
        try {
            const message = JSON.parse(event.data);
            console.log('WebSocket message received:', message);
            
            // 处理心跳响应
            if (message.type === 'pong') {
                return;
            }
            
            // 分发事件到对应的处理器
            this.emit('message', message);
            
            // 根据消息类型分发到特定处理器
            if (message.type) {
                this.emit(message.type, message.data, message.timestamp);
            }
            
        } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
            this.emit('error', error);
        }
    }

    /**
     * WebSocket连接关闭事件
     */
    onClose(event) {
        console.log('WebSocket disconnected:', event.code, event.reason);
        this.isConnected = false;
        this.stopHeartbeat();
        
        this.emit('disconnected', event);
        
        // 如果不是主动断开，尝试重连
        if (!this.isReconnecting && event.code !== 1000) {
            this.attemptReconnect();
        }
    }

    /**
     * WebSocket错误事件
     */
    onError(error) {
        console.error('WebSocket error:', error);
        this.emit('error', error);
    }

    /**
     * 尝试重新连接
     */
    attemptReconnect() {
        if (this.isReconnecting || this.reconnectAttempts >= this.maxReconnectAttempts) {
            return;
        }
        
        this.isReconnecting = true;
        this.reconnectAttempts++;
        
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        
        this.emit('reconnecting', { attempt: this.reconnectAttempts, delay });
        
        setTimeout(() => {
            if (this.isReconnecting) {
                this.connect();
            }
        }, delay);
    }

    /**
     * 发送消息到服务器
     */
    send(message) {
        if (!this.isConnected || !this.ws) {
            console.warn('WebSocket not connected, message not sent:', message);
            return false;
        }
        
        try {
            const data = typeof message === 'string' ? message : JSON.stringify(message);
            this.ws.send(data);
            return true;
        } catch (error) {
            console.error('Failed to send WebSocket message:', error);
            this.emit('error', error);
            return false;
        }
    }

    /**
     * 订阅事件类型
     */
    subscribe(eventType) {
        const message = {
            type: 'subscribe',
            event: eventType
        };
        return this.send(message);
    }

    /**
     * 取消订阅事件类型
     */
    unsubscribe(eventType) {
        const message = {
            type: 'unsubscribe',
            event: eventType
        };
        return this.send(message);
    }

    /**
     * 订阅所有事件类型
     */
    subscribeToAllEvents() {
        const eventTypes = [
            'door_lock_changed',
            'door_state_changed',
            'window_position_changed',
            'light_state_changed',
            'seat_position_changed',
            'seat_memory_save_confirm'
        ];
        
        eventTypes.forEach(eventType => {
            this.subscribe(eventType);
        });
    }

    /**
     * 启动心跳检测
     */
    startHeartbeat() {
        this.stopHeartbeat();
        this.heartbeatTimer = setInterval(() => {
            if (this.isConnected) {
                this.send({ type: 'ping' });
            }
        }, this.heartbeatInterval);
    }

    /**
     * 停止心跳检测
     */
    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }

    /**
     * 事件监听器管理
     */
    on(eventType, handler) {
        if (!this.eventHandlers.has(eventType)) {
            this.eventHandlers.set(eventType, []);
        }
        this.eventHandlers.get(eventType).push(handler);
    }

    off(eventType, handler) {
        if (this.eventHandlers.has(eventType)) {
            const handlers = this.eventHandlers.get(eventType);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }

    emit(eventType, ...args) {
        if (this.eventHandlers.has(eventType)) {
            this.eventHandlers.get(eventType).forEach(handler => {
                try {
                    handler(...args);
                } catch (error) {
                    console.error(`Error in event handler for ${eventType}:`, error);
                }
            });
        }
    }

    /**
     * 获取连接状态
     */
    getConnectionState() {
        return {
            isConnected: this.isConnected,
            isReconnecting: this.isReconnecting,
            reconnectAttempts: this.reconnectAttempts,
            readyState: this.ws ? this.ws.readyState : WebSocket.CLOSED
        };
    }

    /**
     * 获取连接状态文本
     */
    getConnectionStateText() {
        if (this.isConnected) return 'Connected';
        if (this.isReconnecting) return 'Reconnecting';
        return 'Disconnected';
    }
}

// 导出WebSocket客户端
window.BodyControllerWebSocket = BodyControllerWebSocket;

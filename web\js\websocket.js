/**
 * Body Controller WebSocket Client
 * 处理与后端WebSocket服务器的实时通信
 */

class BodyControllerWebSocket {
    constructor(url = 'http://localhost:8080/api/events') {
        this.url = url;
        this.eventSource = null;
        this.eventHandlers = new Map();
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // 初始重连延迟1秒
        this.isConnected = false;
        this.isReconnecting = false;

        // 绑定事件处理器
        this.onOpen = this.onOpen.bind(this);
        this.onMessage = this.onMessage.bind(this);
        this.onError = this.onError.bind(this);
    }

    /**
     * 连接事件流服务器 (Server-Sent Events)
     */
    connect() {
        if (this.eventSource && this.eventSource.readyState === EventSource.OPEN) {
            console.log('EventSource already connected');
            return;
        }

        if (!window.EventSource) {
            console.error('EventSource not supported by this browser');
            this.emit('error', new Error('EventSource not supported'));
            return;
        }

        try {
            console.log(`Connecting to EventSource: ${this.url}`);
            this.eventSource = new EventSource(this.url);

            this.eventSource.onopen = this.onOpen;
            this.eventSource.onmessage = this.onMessage;
            this.eventSource.onerror = this.onError;

        } catch (error) {
            console.error('Failed to create EventSource connection:', error);
            this.emit('error', error);
        }
    }

    /**
     * 断开事件流连接
     */
    disconnect() {
        this.isReconnecting = false;

        if (this.eventSource) {
            this.eventSource.onopen = null;
            this.eventSource.onmessage = null;
            this.eventSource.onerror = null;

            this.eventSource.close();
            this.eventSource = null;
        }

        this.isConnected = false;
        this.emit('disconnected');
    }

    /**
     * 事件流连接打开事件
     */
    onOpen(event) {
        console.log('EventSource connected');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.isReconnecting = false;

        this.emit('connected', event);
    }

    /**
     * 事件流消息接收事件
     */
    onMessage(event) {
        try {
            const message = JSON.parse(event.data);
            console.log('EventSource message received:', message);

            // 分发事件到对应的处理器
            this.emit('message', message);

            // 根据消息类型分发到特定处理器
            if (message.type) {
                this.emit(message.type, message.data, message.timestamp);
            }

        } catch (error) {
            console.error('Failed to parse EventSource message:', error);
            this.emit('error', error);
        }
    }

    /**
     * 事件流错误事件
     */
    onError(error) {
        console.error('EventSource error:', error);
        this.isConnected = false;
        this.emit('error', error);

        // EventSource会自动重连，但我们也可以手动处理
        if (!this.isReconnecting) {
            this.attemptReconnect();
        }
    }

    /**
     * 尝试重新连接
     */
    attemptReconnect() {
        if (this.isReconnecting || this.reconnectAttempts >= this.maxReconnectAttempts) {
            return;
        }
        
        this.isReconnecting = true;
        this.reconnectAttempts++;
        
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        
        this.emit('reconnecting', { attempt: this.reconnectAttempts, delay });
        
        setTimeout(() => {
            if (this.isReconnecting) {
                this.connect();
            }
        }, delay);
    }

    // EventSource是单向的，不需要发送消息
    // 所有事件都会自动接收

    // EventSource不需要心跳检测，浏览器会自动处理

    /**
     * 事件监听器管理
     */
    on(eventType, handler) {
        if (!this.eventHandlers.has(eventType)) {
            this.eventHandlers.set(eventType, []);
        }
        this.eventHandlers.get(eventType).push(handler);
    }

    off(eventType, handler) {
        if (this.eventHandlers.has(eventType)) {
            const handlers = this.eventHandlers.get(eventType);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }

    emit(eventType, ...args) {
        if (this.eventHandlers.has(eventType)) {
            this.eventHandlers.get(eventType).forEach(handler => {
                try {
                    handler(...args);
                } catch (error) {
                    console.error(`Error in event handler for ${eventType}:`, error);
                }
            });
        }
    }

    /**
     * 获取连接状态
     */
    getConnectionState() {
        return {
            isConnected: this.isConnected,
            isReconnecting: this.isReconnecting,
            reconnectAttempts: this.reconnectAttempts,
            readyState: this.eventSource ? this.eventSource.readyState : EventSource.CLOSED
        };
    }

    /**
     * 获取连接状态文本
     */
    getConnectionStateText() {
        if (this.isConnected) return 'Connected';
        if (this.isReconnecting) return 'Reconnecting';
        return 'Disconnected';
    }
}

// 导出WebSocket客户端
window.BodyControllerWebSocket = BodyControllerWebSocket;
